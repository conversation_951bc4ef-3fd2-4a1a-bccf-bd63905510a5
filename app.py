#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宠物服务平台 Flask API 应用
支持用户端和商家端的完整功能
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from datetime import datetime, timedelta
import jwt
import hashlib
import random
import string
import json
import os
from functools import wraps

# 创建 Flask 应用
app = Flask(__name__)
app.config['SECRET_KEY'] = 'pet-service-platform-secret-key-2024'
CORS(app)  # 允许跨域请求

# 模拟数据存储（实际项目中应使用数据库）
users_db = {}
pets_db = {}
merchants_db = {}
services_db = {}
orders_db = {}
appointments_db = {}
evaluations_db = {}
sms_codes = {}

# JWT 配置
JWT_SECRET = app.config['SECRET_KEY']
JWT_ALGORITHM = 'HS256'

# 工具函数
def generate_id(prefix, length=8):
    """生成唯一ID"""
    suffix = ''.join(random.choices(string.digits, k=length-1))
    return f"{prefix}{suffix}"

def generate_sms_code():
    """生成6位短信验证码"""
    return ''.join(random.choices(string.digits, k=6))

def hash_password(password):
    """密码哈希"""
    return hashlib.sha256(password.encode()).hexdigest()

def create_token(user_id, user_type='user'):
    """创建JWT Token"""
    payload = {
        'user_id': user_id,
        'user_type': user_type,
        'exp': datetime.utcnow() + timedelta(days=7)
    }
    return jwt.encode(payload, JWT_SECRET, algorithm=JWT_ALGORITHM)

def verify_token(token):
    """验证JWT Token"""
    try:
        payload = jwt.decode(token, JWT_SECRET, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        return None
    except jwt.InvalidTokenError:
        return None

# 装饰器
def token_required(f):
    """Token验证装饰器"""
    @wraps(f)
    def decorated(*args, **kwargs):
        token = None
        auth_header = request.headers.get('Authorization')
        
        if auth_header:
            try:
                token = auth_header.split(' ')[1]
            except IndexError:
                return jsonify({
                    'code': 401,
                    'data': None,
                    'message': 'Token格式错误'
                }), 401

        if not token:
            return jsonify({
                'code': 401,
                'data': None,
                'message': '缺少认证Token'
            }), 401

        payload = verify_token(token)
        if not payload:
            return jsonify({
                'code': 401,
                'data': None,
                'message': 'Token无效或已过期'
            }), 401
        
        request.current_user = payload
        return f(*args, **kwargs)
    
    return decorated

def success_response(data=None, message="操作成功"):
    """成功响应格式"""
    return jsonify({
        'code': 200,
        'data': data,
        'message': message
    })

def error_response(code=400, message="请求失败", data=None):
    """错误响应格式"""
    return jsonify({
        'code': code,
        'data': data,
        'message': message
    }), code

# ==================== 用户端 API ====================

# 用户认证模块
@app.route('/api/auth/send-code', methods=['POST'])
def send_sms_code():
    """发送短信验证码"""
    data = request.get_json()
    phone = data.get('phone')
    
    if not phone or len(phone) != 11:
        return error_response(10001, '手机号格式错误')
    
    # 生成验证码
    code = generate_sms_code()
    expire_time = datetime.now() + timedelta(minutes=5)
    
    # 存储验证码（实际项目中应发送真实短信）
    sms_codes[phone] = {
        'code': code,
        'expire_time': expire_time,
        'type': 'login'
    }
    
    print(f"短信验证码: {phone} -> {code}")  # 调试用，实际项目中删除
    
    return success_response({
        'expire_time': 300
    }, '验证码发送成功')

@app.route('/api/auth/register', methods=['POST'])
def user_register():
    """用户注册"""
    data = request.get_json()
    phone = data.get('phone')
    verification_code = data.get('verification_code')
    
    if not phone or not verification_code:
        return error_response(400, '手机号和验证码不能为空')
    
    # 验证短信验证码
    if phone not in sms_codes:
        return error_response(10002, '验证码不存在或已过期')
    
    sms_data = sms_codes[phone]
    if sms_data['code'] != verification_code:
        return error_response(10002, '验证码错误')
    
    if datetime.now() > sms_data['expire_time']:
        return error_response(10002, '验证码已过期')
    
    # 检查用户是否已存在
    for user in users_db.values():
        if user['user_phone'] == phone:
            return error_response(10004, '用户已存在')
    
    # 创建新用户（严格按照数据库表结构）
    user_id = generate_id('U')
    user_info = {
        'user_id': user_id,                    # varchar(8)
        'user_phone': phone,                   # char(11)
        'user_verification_code': None,        # char(6)
        'user_name': phone,                    # varchar(20) 默认用户名为手机号
        'user_sex': None                       # enum 0-男，1-女
    }
    
    users_db[user_id] = user_info
    
    # 删除已使用的验证码
    del sms_codes[phone]
    
    # 生成Token
    token = create_token(user_id, 'user')
    
    return success_response({
        'user_id': user_id,
        'token': token,
        'user_info': user_info
    }, '注册成功')

@app.route('/api/auth/login', methods=['POST'])
def user_login():
    """用户登录"""
    data = request.get_json()
    phone = data.get('phone')
    verification_code = data.get('verification_code')
    
    if not phone or not verification_code:
        return error_response(400, '手机号和验证码不能为空')
    
    # 验证短信验证码
    if phone not in sms_codes:
        return error_response(10002, '验证码不存在或已过期')
    
    sms_data = sms_codes[phone]
    if sms_data['code'] != verification_code:
        return error_response(10002, '验证码错误')
    
    if datetime.now() > sms_data['expire_time']:
        return error_response(10002, '验证码已过期')
    
    # 查找用户
    user_info = None
    for user in users_db.values():
        if user['user_phone'] == phone:
            user_info = user
            break
    
    if not user_info:
        return error_response(10003, '用户不存在，请先注册')
    
    # 删除已使用的验证码
    del sms_codes[phone]
    
    # 生成Token
    token = create_token(user_info['user_id'], 'user')
    
    return success_response({
        'user_id': user_info['user_id'],
        'token': token,
        'user_info': user_info
    }, '登录成功')

@app.route('/api/user/profile', methods=['PUT'])
@token_required
def update_user_profile():
    """更新用户信息"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')
    
    user_id = request.current_user['user_id']
    data = request.get_json()
    
    if user_id not in users_db:
        return error_response(10003, '用户不存在')
    
    user_info = users_db[user_id]
    
    # 更新用户信息（严格按照数据库表结构）
    if 'user_name' in data:
        if len(data['user_name']) < 2 or len(data['user_name']) > 20:
            return error_response(400, '用户名长度应在2-20个字符之间')
        user_info['user_name'] = data['user_name']

    if 'user_sex' in data:
        if data['user_sex'] not in [0, 1]:  # enum 0-男，1-女
            return error_response(400, '性别参数错误')
        user_info['user_sex'] = data['user_sex']
    
    return success_response(user_info, '更新成功')

# 宠物管理模块
@app.route('/api/pets', methods=['POST'])
@token_required
def add_pet():
    """添加宠物"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    required_fields = ['pet_kind', 'pet_age', 'pet_weight']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    # 创建宠物信息（严格按照数据库表结构）
    pet_id = generate_id('P')
    pet_info = {
        'pet_id': pet_id,
        'user_id': user_id,
        'pet_kind': data['pet_kind'],  # varchar(5)
        'pet_age': data['pet_age'],    # date
        'pet_weight': data['pet_weight']  # varchar(5)
    }

    pets_db[pet_id] = pet_info

    return success_response(pet_info, '添加成功')

@app.route('/api/pets', methods=['GET'])
@token_required
def get_pets():
    """获取宠物列表"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    # 获取用户的宠物列表（只返回数据库表字段）
    user_pets = []
    for pet in pets_db.values():
        if pet['user_id'] == user_id:
            # 只返回数据库表中定义的字段
            pet_data = {
                'pet_id': pet['pet_id'],
                'user_id': pet['user_id'],
                'pet_kind': pet['pet_kind'],
                'pet_age': pet['pet_age'],
                'pet_weight': pet['pet_weight']
            }
            user_pets.append(pet_data)

    return success_response(user_pets, '获取成功')

@app.route('/api/pets/<pet_id>', methods=['DELETE'])
@token_required
def delete_pet(pet_id):
    """删除宠物"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if pet_id not in pets_db:
        return error_response(20001, '宠物不存在')

    pet_info = pets_db[pet_id]
    if pet_info['user_id'] != user_id:
        return error_response(403, '无权删除此宠物')

    # 直接删除
    del pets_db[pet_id]

    return success_response(None, '删除成功')

# 服务模块
@app.route('/api/services', methods=['GET'])
def get_services():
    """获取服务列表"""
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    service_kind = request.args.get('service_kind')
    keyword = request.args.get('keyword', '')

    # 过滤服务（移除is_active检查，因为数据库表中没有此字段）
    filtered_services = []
    for service in services_db.values():

        # 按类型过滤
        if service_kind is not None and str(service['service_kind']) != str(service_kind):
            continue

        # 按关键词过滤
        if keyword and keyword not in service['service_name']:
            continue

        filtered_services.append(service)

    # 分页
    total = len(filtered_services)
    start = (page - 1) * limit
    end = start + limit
    services = filtered_services[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'services': services
    }, '获取成功')

@app.route('/api/services/<service_id>', methods=['GET'])
def get_service_detail(service_id):
    """获取服务详情"""
    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    # 移除is_active检查，因为数据库表中没有此字段

    return success_response(service, '获取成功')

# 订单模块
@app.route('/api/orders', methods=['POST'])
@token_required
def create_order():
    """创建订单"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    service_id = data.get('service_id')
    pet_id = data.get('pet_id')

    if not service_id or not pet_id:
        return error_response(400, '服务ID和宠物ID不能为空')

    # 验证服务存在
    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    # 移除is_active检查，因为数据库表中没有此字段

    # 验证宠物存在且属于当前用户
    if pet_id not in pets_db:
        return error_response(20001, '宠物不存在')

    pet = pets_db[pet_id]
    if pet['user_id'] != user_id:
        return error_response(403, '无权使用此宠物')

    # 创建订单（严格按照数据库表结构）
    order_id = generate_id('O', 11)
    order_info = {
        'order_id': order_id,                  # varchar(11)
        'user_id': user_id,                    # varchar(8)
        'service_id': service_id,              # varchar(11)
        'order_status': 4,                     # enum 0-已取消，1-已支付，2-已预约，3-已完成，4-未支付
        'order_price': service['service_price'], # Decimal(10,2)
        'pet_id': pet_id                       # varchar(8)
    }

    orders_db[order_id] = order_info

    # 模拟支付URL
    payment_url = f"https://pay.alipay.com/order/{order_id}"

    return success_response({
        **order_info,
        'payment_url': payment_url
    }, '订单创建成功')

@app.route('/api/orders/<order_id>/payment-callback', methods=['POST'])
def payment_callback(order_id):
    """支付回调"""
    data = request.get_json()

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]

    # 模拟支付成功
    payment_status = data.get('payment_status', 1)
    if payment_status == 1:
        order['order_status'] = 1  # 已支付
        order['updated_at'] = datetime.now().isoformat()

        return success_response({
            'order_id': order_id,
            'order_status': 1
        }, '支付成功')
    else:
        return error_response(40003, '支付失败')

@app.route('/api/orders', methods=['GET'])
@token_required
def get_orders():
    """获取订单列表"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    order_status = request.args.get('order_status')

    # 过滤用户订单
    user_orders = []
    for order in orders_db.values():
        if order['user_id'] != user_id:
            continue

        if order_status is not None and str(order['order_status']) != str(order_status):
            continue

        # 添加关联信息
        order_detail = order.copy()
        if order['service_id'] in services_db:
            service = services_db[order['service_id']]
            order_detail['service_name'] = service['service_name']

        if order['pet_id'] in pets_db:
            pet = pets_db[order['pet_id']]
            order_detail['pet_kind'] = pet['pet_kind']

        user_orders.append(order_detail)

    # 按订单ID倒序排序（因为数据库表中没有created_at字段）
    user_orders.sort(key=lambda x: x['order_id'], reverse=True)

    # 分页
    total = len(user_orders)
    start = (page - 1) * limit
    end = start + limit
    orders = user_orders[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'orders': orders
    }, '获取成功')

@app.route('/api/orders/<order_id>', methods=['GET'])
@token_required
def get_order_detail(order_id):
    """获取订单详情"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权查看此订单')

    # 添加关联信息
    order_detail = order.copy()

    if order['service_id'] in services_db:
        service = services_db[order['service_id']]
        order_detail.update({
            'service_name': service['service_name'],
            'service_description': service['service_description']
        })

    if order['pet_id'] in pets_db:
        pet = pets_db[order['pet_id']]
        order_detail.update({
            'pet_kind': pet['pet_kind'],
            'pet_weight': pet['pet_weight'],
            'pet_age': pet['pet_age']
        })

    # 查找预约信息
    appointment_info = None
    for appointment in appointments_db.values():
        if appointment['order_id'] == order_id:
            appointment_info = appointment
            break

    if appointment_info:
        order_detail['appointment_info'] = appointment_info

    return success_response(order_detail, '获取成功')

@app.route('/api/orders/<order_id>/cancel', methods=['PUT'])
@token_required
def cancel_order(order_id):
    """取消订单"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权取消此订单')

    if order['order_status'] in [0, 3]:  # 已取消或已完成
        return error_response(40002, '订单状态不允许取消')

    # 取消订单
    order['order_status'] = 0  # 已取消
    order['updated_at'] = datetime.now().isoformat()

    # 如果已支付，计算退款金额
    refund_amount = 0
    if order['order_status'] == 1:  # 已支付
        refund_amount = order['order_price']

    return success_response({
        'order_id': order_id,
        'order_status': 0,
        'refund_amount': refund_amount
    }, '订单取消成功')

# 预约模块
@app.route('/api/appointments', methods=['POST'])
@token_required
def create_appointment():
    """创建预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    order_id = data.get('order_id')
    appointment_time = data.get('appointment_time')
    appointment_location = data.get('appointment_location')

    if not all([order_id, appointment_time, appointment_location]):
        return error_response(400, '订单ID、预约时间和地点不能为空')

    # 验证订单
    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权预约此订单')

    if order['order_status'] != 1:  # 必须是已支付状态
        return error_response(40002, '订单状态不允许预约')

    # 创建预约（严格按照数据库表结构）
    appointment_id = generate_id('A', 11)
    appointment_info = {
        'appointment_id': appointment_id,      # varchar(11)
        'order_id': order_id,                  # varchar(11)
        'appointment_time': appointment_time,  # datetime
        'appointment_location': appointment_location, # text
        'appointment_status': 0,               # enum 预约状态
        'user_id': user_id                     # varchar(8) 注意：这是主键
    }

    appointments_db[appointment_id] = appointment_info

    # 更新订单状态
    order['order_status'] = 2  # 已预约
    order['updated_at'] = datetime.now().isoformat()

    return success_response(appointment_info, '预约成功')

@app.route('/api/appointments/<appointment_id>', methods=['PUT'])
@token_required
def update_appointment(appointment_id):
    """修改预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['user_id'] != user_id:
        return error_response(403, '无权修改此预约')

    if appointment['appointment_status'] in [2, 3]:  # 已取消或已完成
        return error_response(40002, '预约状态不允许修改')

    # 更新预约信息
    if 'appointment_time' in data:
        appointment['appointment_time'] = data['appointment_time']

    if 'appointment_location' in data:
        appointment['appointment_location'] = data['appointment_location']

    appointment['updated_at'] = datetime.now().isoformat()

    return success_response(appointment, '预约修改成功')

@app.route('/api/appointments/<appointment_id>/cancel', methods=['PUT'])
@token_required
def cancel_appointment(appointment_id):
    """取消预约"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['user_id'] != user_id:
        return error_response(403, '无权取消此预约')

    # 取消预约
    appointment['appointment_status'] = 2  # 已取消
    appointment['updated_at'] = datetime.now().isoformat()

    return success_response({
        'appointment_id': appointment_id,
        'appointment_status': 2
    }, '预约取消成功')

# 评价模块
@app.route('/api/evaluations', methods=['POST'])
@token_required
def create_evaluation():
    """提交评价"""
    if request.current_user['user_type'] != 'user':
        return error_response(403, '权限不足')

    user_id = request.current_user['user_id']
    data = request.get_json()

    order_id = data.get('order_id')
    evaluation_star = data.get('evaluation_star')
    evaluation_context = data.get('evaluation_context', '')
    evaluation_picture = data.get('evaluation_picture', [])

    if not order_id or not evaluation_star:
        return error_response(400, '订单ID和评分不能为空')

    if evaluation_star < 1 or evaluation_star > 5:
        return error_response(400, '评分必须在1-5之间')

    # 验证订单
    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['user_id'] != user_id:
        return error_response(403, '无权评价此订单')

    if order['order_status'] != 3:  # 必须是已完成状态
        return error_response(40002, '订单状态不允许评价')

    # 检查是否已评价
    for evaluation in evaluations_db.values():
        if evaluation['order_id'] == order_id:
            return error_response(400, '该订单已评价')

    # 创建评价（严格按照数据库表结构）
    evaluation_id = generate_id('E', 11)
    evaluation_info = {
        'evaluation_id': evaluation_id,        # varchar(11)
        'user_id': user_id,                    # varchar(8)
        'order_id': order_id,                  # varchar(11)
        'evaluation_star': evaluation_star,    # tinyint unsigned
        'evaluation_context': evaluation_context, # text
        'evaluation_picture': evaluation_picture  # text
    }

    evaluations_db[evaluation_id] = evaluation_info

    return success_response(evaluation_info, '评价提交成功')

@app.route('/api/services/<service_id>/evaluations', methods=['GET'])
def get_service_evaluations(service_id):
    """获取服务评价列表"""
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))

    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    # 获取该服务的评价（通过订单关联）
    service_evaluations = []
    total_stars = 0
    for evaluation in evaluations_db.values():
        # 通过订单ID找到对应的服务
        order_id = evaluation['order_id']
        if order_id in orders_db:
            order = orders_db[order_id]
            if order['service_id'] == service_id:
                # 添加用户信息
                evaluation_detail = evaluation.copy()
                if evaluation['user_id'] in users_db:
                    user = users_db[evaluation['user_id']]
                    evaluation_detail['user_name'] = user['user_name']

                service_evaluations.append(evaluation_detail)
                total_stars += evaluation['evaluation_star']

    # 计算平均评分
    average_star = round(total_stars / len(service_evaluations), 1) if service_evaluations else 0

    # 按评价ID倒序排序（因为数据库表中没有created_at字段）
    service_evaluations.sort(key=lambda x: x['evaluation_id'], reverse=True)

    # 分页
    total = len(service_evaluations)
    start = (page - 1) * limit
    end = start + limit
    evaluations = service_evaluations[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'average_star': average_star,
        'evaluations': evaluations
    }, '获取成功')

# ==================== 商家端 API ====================

# 商家认证模块
@app.route('/api/merchant/auth/register', methods=['POST'])
def merchant_register():
    """商家注册"""
    data = request.get_json()

    required_fields = ['merchant_account', 'merchant_password', 'merchant_name']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    merchant_account = data['merchant_account']
    merchant_password = data['merchant_password']

    # 检查账号是否已存在
    for merchant in merchants_db.values():
        if merchant['merchant_account'] == merchant_account:
            return error_response(60001, '商家账号已存在')

    # 验证账号格式
    if len(merchant_account) > 50 or not merchant_account.isalnum():
        return error_response(400, '账号格式错误')

    if len(merchant_password) > 50:
        return error_response(400, '密码长度不能超过50个字符')

    # 创建商家（严格按照数据库表结构）
    merchant_id = generate_id('M')
    merchant_info = {
        'merchant_id': merchant_id,                                    # varchar(8)
        'merchant_account': merchant_account,                          # varchar(50)
        'merchant_password': hash_password(merchant_password),         # varchar(50)
        'merchant_name': data['merchant_name'],                        # varchar(200)
        'merchant_phone': data.get('merchant_phone', ''),              # char(11)
        'merchant_location': data.get('merchant_location', '')         # text
    }

    merchants_db[merchant_id] = merchant_info

    # 生成Token
    token = create_token(merchant_id, 'merchant')

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response({
        'merchant_id': merchant_id,
        'token': token,
        'merchant_info': merchant_response
    }, '注册成功')

@app.route('/api/merchant/auth/login', methods=['POST'])
def merchant_login():
    """商家登录"""
    data = request.get_json()

    merchant_account = data.get('merchant_account')
    merchant_password = data.get('merchant_password')

    if not merchant_account or not merchant_password:
        return error_response(400, '账号和密码不能为空')

    # 查找商家
    merchant_info = None
    for merchant in merchants_db.values():
        if merchant['merchant_account'] == merchant_account:
            merchant_info = merchant
            break

    if not merchant_info:
        return error_response(60002, '商家账号不存在')

    # 验证密码
    if merchant_info['merchant_password'] != hash_password(merchant_password):
        return error_response(60002, '密码错误')

    # 移除状态检查，因为数据库表中没有status字段

    # 生成Token
    token = create_token(merchant_info['merchant_id'], 'merchant')

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response({
        'merchant_id': merchant_info['merchant_id'],
        'token': token,
        'merchant_info': merchant_response
    }, '登录成功')

@app.route('/api/merchant/profile', methods=['PUT'])
@token_required
def update_merchant_profile():
    """更新商家信息"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if merchant_id not in merchants_db:
        return error_response(60001, '商家不存在')

    merchant_info = merchants_db[merchant_id]

    # 更新商家信息
    if 'merchant_name' in data:
        merchant_info['merchant_name'] = data['merchant_name']

    if 'merchant_phone' in data:
        merchant_info['merchant_phone'] = data['merchant_phone']

    if 'merchant_location' in data:
        merchant_info['merchant_location'] = data['merchant_location']

    if 'merchant_password' in data:
        merchant_info['merchant_password'] = hash_password(data['merchant_password'])

    merchant_info['updated_at'] = datetime.now().isoformat()

    # 返回信息（不包含密码）
    merchant_response = merchant_info.copy()
    del merchant_response['merchant_password']

    return success_response(merchant_response, '更新成功')

# 商家服务管理模块
@app.route('/api/merchant/services', methods=['POST'])
@token_required
def add_merchant_service():
    """添加服务"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    required_fields = ['service_name', 'service_kind', 'service_price', 'service_time']
    for field in required_fields:
        if field not in data:
            return error_response(400, f'缺少必填字段: {field}')

    # 创建服务（严格按照数据库表结构）
    service_id = generate_id('S', 11)
    service_info = {
        'service_id': service_id,                                      # varchar(11)
        'service_name': data['service_name'],                          # varchar(100)
        'service_kind': int(data['service_kind']),                     # enum 0-美容，1-寄养，2-清洁，3-训练
        'service_description': data.get('service_description', ''),    # text
        'service_price': float(data['service_price']),                 # Decimal(10,2)
        'service_time': int(data['service_time']),                     # int 服务时长，精确到分钟
        'service_picture': data.get('service_picture', '')             # text 图片URL
    }

    services_db[service_id] = service_info

    return success_response(service_info, '服务添加成功')

@app.route('/api/merchant/services', methods=['GET'])
@token_required
def get_merchant_services():
    """获取商家服务列表"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    service_kind = request.args.get('service_kind')
    keyword = request.args.get('keyword', '')

    # 过滤商家服务（注意：数据库表中没有merchant_id字段，这里需要其他方式关联）
    merchant_services = []
    for service in services_db.values():
        # 暂时显示所有服务，因为数据库表设计中service表没有merchant_id字段

        # 按类型过滤
        if service_kind is not None and str(service['service_kind']) != str(service_kind):
            continue

        # 按关键词过滤
        if keyword and keyword not in service['service_name']:
            continue

        # 添加统计信息
        service_detail = service.copy()
        order_count = sum(1 for order in orders_db.values() if order['service_id'] == service['service_id'])
        service_detail['order_count'] = order_count

        # 计算平均评分
        evaluations = [e for e in evaluations_db.values() if e['service_id'] == service['service_id']]
        if evaluations:
            avg_rating = sum(e['evaluation_star'] for e in evaluations) / len(evaluations)
            service_detail['average_rating'] = round(avg_rating, 1)
        else:
            service_detail['average_rating'] = 0

        merchant_services.append(service_detail)

    # 按服务ID倒序排序（因为数据库表中没有created_at字段）
    merchant_services.sort(key=lambda x: x['service_id'], reverse=True)

    # 分页
    total = len(merchant_services)
    start = (page - 1) * limit
    end = start + limit
    services = merchant_services[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'services': services
    }, '获取成功')

@app.route('/api/merchant/services/<service_id>', methods=['PUT'])
@token_required
def update_merchant_service(service_id):
    """编辑服务"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    if service['merchant_id'] != merchant_id:
        return error_response(403, '无权编辑此服务')

    # 更新服务信息
    if 'service_name' in data:
        service['service_name'] = data['service_name']

    if 'service_kind' in data:
        service['service_kind'] = int(data['service_kind'])

    if 'service_description' in data:
        service['service_description'] = data['service_description']

    if 'service_price' in data:
        service['service_price'] = float(data['service_price'])

    if 'service_time' in data:
        service['service_time'] = int(data['service_time'])

    if 'service_picture' in data:
        service['service_picture'] = data['service_picture']

    service['updated_at'] = datetime.now().isoformat()

    return success_response(service, '服务更新成功')

@app.route('/api/merchant/services/<service_id>', methods=['DELETE'])
@token_required
def delete_merchant_service(service_id):
    """删除服务"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']

    if service_id not in services_db:
        return error_response(30001, '服务不存在')

    service = services_db[service_id]
    if service['merchant_id'] != merchant_id:
        return error_response(403, '无权删除此服务')

    # 检查是否有未完成的订单
    active_orders = [order for order in orders_db.values()
                    if order['service_id'] == service_id and order['order_status'] in [1, 2]]

    if active_orders:
        return error_response(400, '存在未完成的订单，无法删除服务')

    # 直接删除（因为数据库表中没有is_active字段）
    del services_db[service_id]

    return success_response(None, '服务删除成功')

# 商家订单管理模块
@app.route('/api/merchant/orders', methods=['GET'])
@token_required
def get_merchant_orders():
    """获取商家订单列表"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    order_status = request.args.get('order_status')
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    keyword = request.args.get('keyword', '')

    # 过滤商家订单（注意：数据库表中没有merchant_id字段）
    merchant_orders = []
    for order in orders_db.values():
        # 暂时显示所有订单，因为数据库表设计中order表没有merchant_id字段

        # 按状态过滤
        if order_status is not None and str(order['order_status']) != str(order_status):
            continue

        # 跳过日期过滤（因为数据库表中没有created_at字段）

        # 添加关联信息
        order_detail = order.copy()

        # 用户信息
        if order['user_id'] in users_db:
            user = users_db[order['user_id']]
            order_detail['user_phone'] = user['user_phone']

        # 服务信息
        if order['service_id'] in services_db:
            service = services_db[order['service_id']]
            order_detail['service_name'] = service['service_name']

        # 宠物信息
        if order['pet_id'] in pets_db:
            pet = pets_db[order['pet_id']]
            order_detail.update({
                'pet_kind': pet['pet_kind'],
                'pet_weight': pet['pet_weight']
            })

        # 预约信息
        for appointment in appointments_db.values():
            if appointment['order_id'] == order['order_id']:
                order_detail.update({
                    'appointment_time': appointment['appointment_time'],
                    'appointment_location': appointment['appointment_location']
                })
                break

        # 按关键词过滤（用户手机号、订单号）
        if keyword:
            if (keyword not in order_detail.get('user_phone', '') and
                keyword not in order['order_id']):
                continue

        merchant_orders.append(order_detail)

    # 按订单ID倒序排序（因为数据库表中没有created_at字段）
    merchant_orders.sort(key=lambda x: x['order_id'], reverse=True)

    # 分页
    total = len(merchant_orders)
    start = (page - 1) * limit
    end = start + limit
    orders = merchant_orders[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'orders': orders
    }, '获取成功')

@app.route('/api/merchant/orders/<order_id>/confirm', methods=['PUT'])
@token_required
def confirm_merchant_order(order_id):
    """确认订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] != 2:  # 必须是已预约状态
        return error_response(40002, '订单状态不允许确认')

    # 确认订单（这里可以添加更多业务逻辑）
    order['updated_at'] = datetime.now().isoformat()

    return success_response({
        'order_id': order_id,
        'order_status': order['order_status']
    }, '订单确认成功')

@app.route('/api/merchant/orders/<order_id>/complete', methods=['PUT'])
@token_required
def complete_merchant_order(order_id):
    """完成订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] != 2:  # 必须是已预约状态
        return error_response(40002, '订单状态不允许完成')

    # 完成订单
    order['order_status'] = 3  # 已完成
    order['updated_at'] = datetime.now().isoformat()

    return success_response({
        'order_id': order_id,
        'order_status': 3
    }, '订单完成')

@app.route('/api/merchant/orders/<order_id>/cancel', methods=['PUT'])
@token_required
def cancel_merchant_order(order_id):
    """商家取消订单"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if order_id not in orders_db:
        return error_response(40001, '订单不存在')

    order = orders_db[order_id]
    if order['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此订单')

    if order['order_status'] in [0, 3]:  # 已取消或已完成
        return error_response(40002, '订单状态不允许取消')

    # 取消订单
    order['order_status'] = 0  # 已取消
    order['cancel_reason'] = data.get('cancel_reason', '商家取消')
    order['updated_at'] = datetime.now().isoformat()

    # 计算补偿金额（120%）
    compensation_rate = 1.2
    refund_amount = order['order_price'] * compensation_rate

    return success_response({
        'order_id': order_id,
        'order_status': 0,
        'refund_amount': refund_amount,
        'compensation_rate': compensation_rate
    }, '订单取消成功')

# 商家预约管理模块
@app.route('/api/merchant/appointments', methods=['GET'])
@token_required
def get_merchant_appointments():
    """获取商家预约列表"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    page = int(request.args.get('page', 1))
    limit = int(request.args.get('limit', 10))
    appointment_status = request.args.get('appointment_status')
    date = request.args.get('date')
    keyword = request.args.get('keyword', '')

    # 过滤商家预约
    merchant_appointments = []
    for appointment in appointments_db.values():
        if appointment['merchant_id'] != merchant_id:
            continue

        # 按状态过滤
        if appointment_status is not None and str(appointment['appointment_status']) != str(appointment_status):
            continue

        # 按日期过滤
        if date and not appointment['appointment_time'].startswith(date):
            continue

        # 添加关联信息
        appointment_detail = appointment.copy()

        # 订单信息
        if appointment['order_id'] in orders_db:
            order = orders_db[appointment['order_id']]

            # 用户信息
            if order['user_id'] in users_db:
                user = users_db[order['user_id']]
                appointment_detail['user_phone'] = user['user_phone']

            # 服务信息
            if order['service_id'] in services_db:
                service = services_db[order['service_id']]
                appointment_detail['service_name'] = service['service_name']

            # 宠物信息
            if order['pet_id'] in pets_db:
                pet = pets_db[order['pet_id']]
                appointment_detail.update({
                    'pet_kind': pet['pet_kind'],
                    'pet_weight': pet['pet_weight']
                })

        # 按关键词过滤
        if keyword:
            if (keyword not in appointment_detail.get('user_phone', '') and
                keyword not in appointment_detail.get('service_name', '')):
                continue

        merchant_appointments.append(appointment_detail)

    # 按预约时间排序
    merchant_appointments.sort(key=lambda x: x['appointment_time'])

    # 分页
    total = len(merchant_appointments)
    start = (page - 1) * limit
    end = start + limit
    appointments = merchant_appointments[start:end]

    return success_response({
        'total': total,
        'page': page,
        'limit': limit,
        'appointments': appointments
    }, '获取成功')

@app.route('/api/merchant/appointments/<appointment_id>/confirm', methods=['PUT'])
@token_required
def confirm_merchant_appointment(appointment_id):
    """确认预约"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此预约')

    if appointment['appointment_status'] != 0:  # 必须是待确认状态
        return error_response(40002, '预约状态不允许确认')

    # 确认预约
    appointment['appointment_status'] = 1  # 已确认
    appointment['merchant_notes'] = data.get('notes', '')
    appointment['updated_at'] = datetime.now().isoformat()

    return success_response({
        'appointment_id': appointment_id,
        'appointment_status': 1,
        'notes': appointment['merchant_notes']
    }, '预约确认成功')

@app.route('/api/merchant/appointments/<appointment_id>', methods=['PUT'])
@token_required
def update_merchant_appointment(appointment_id):
    """修改预约"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此预约')

    if appointment['appointment_status'] in [2, 3]:  # 已取消或已完成
        return error_response(40002, '预约状态不允许修改')

    # 更新预约信息
    if 'appointment_time' in data:
        appointment['appointment_time'] = data['appointment_time']

    if 'appointment_location' in data:
        appointment['appointment_location'] = data['appointment_location']

    if 'notes' in data:
        appointment['merchant_notes'] = data['notes']

    appointment['updated_at'] = datetime.now().isoformat()

    return success_response({
        'appointment_id': appointment_id,
        'appointment_time': appointment['appointment_time'],
        'appointment_location': appointment['appointment_location'],
        'notes': appointment.get('merchant_notes', '')
    }, '预约修改成功')

@app.route('/api/merchant/appointments/<appointment_id>/cancel', methods=['PUT'])
@token_required
def cancel_merchant_appointment(appointment_id):
    """商家取消预约"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    data = request.get_json()

    if appointment_id not in appointments_db:
        return error_response(50002, '预约不存在')

    appointment = appointments_db[appointment_id]
    if appointment['merchant_id'] != merchant_id:
        return error_response(403, '无权操作此预约')

    # 取消预约
    appointment['appointment_status'] = 2  # 已取消
    appointment['cancel_reason'] = data.get('cancel_reason', '商家取消')
    appointment['updated_at'] = datetime.now().isoformat()

    # 计算补偿
    compensation_rate = data.get('compensation_rate', 1.2)

    # 获取订单信息计算退款
    order = orders_db.get(appointment['order_id'])
    refund_amount = 0
    if order:
        refund_amount = order['order_price'] * compensation_rate

    return success_response({
        'appointment_id': appointment_id,
        'appointment_status': 2,
        'refund_amount': refund_amount,
        'compensation_rate': compensation_rate
    }, '预约取消成功')

# 商家数据统计模块
@app.route('/api/merchant/statistics', methods=['GET'])
@token_required
def get_merchant_statistics():
    """获取商家统计数据"""
    if request.current_user['user_type'] != 'merchant':
        return error_response(403, '权限不足')

    merchant_id = request.current_user['user_id']
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # 过滤商家订单
    merchant_orders = [order for order in orders_db.values() if order['merchant_id'] == merchant_id]

    # 按日期过滤
    if start_date:
        merchant_orders = [order for order in merchant_orders if order['created_at'] >= start_date]
    if end_date:
        merchant_orders = [order for order in merchant_orders if order['created_at'] <= end_date]

    # 统计数据
    total_orders = len(merchant_orders)
    completed_orders = len([order for order in merchant_orders if order['order_status'] == 3])
    cancelled_orders = len([order for order in merchant_orders if order['order_status'] == 0])
    pending_orders = len([order for order in merchant_orders if order['order_status'] in [1, 2]])

    # 计算总收入
    total_revenue = sum(order['order_price'] for order in merchant_orders if order['order_status'] == 3)

    # 计算平均评分
    merchant_evaluations = [e for e in evaluations_db.values() if e['merchant_id'] == merchant_id]
    average_rating = 0
    if merchant_evaluations:
        total_stars = sum(e['evaluation_star'] for e in merchant_evaluations)
        average_rating = round(total_stars / len(merchant_evaluations), 1)

    # 服务统计
    service_statistics = []
    merchant_services = [s for s in services_db.values() if s['merchant_id'] == merchant_id]

    for service in merchant_services:
        service_orders = [order for order in merchant_orders if order['service_id'] == service['service_id']]
        service_revenue = sum(order['order_price'] for order in service_orders if order['order_status'] == 3)

        service_evaluations = [e for e in merchant_evaluations if e['service_id'] == service['service_id']]
        service_rating = 0
        if service_evaluations:
            service_rating = round(sum(e['evaluation_star'] for e in service_evaluations) / len(service_evaluations), 1)

        service_statistics.append({
            'service_name': service['service_name'],
            'order_count': len(service_orders),
            'revenue': service_revenue,
            'average_rating': service_rating
        })

    return success_response({
        'total_orders': total_orders,
        'completed_orders': completed_orders,
        'cancelled_orders': cancelled_orders,
        'pending_orders': pending_orders,
        'total_revenue': total_revenue,
        'average_rating': average_rating,
        'service_statistics': service_statistics
    }, '获取成功')

# ==================== 通用工具接口 ====================

@app.route('/api/upload', methods=['POST'])
@token_required
def upload_file():
    """文件上传"""
    if 'file' not in request.files:
        return error_response(70001, '没有文件')

    file = request.files['file']
    file_type = request.form.get('type', 'general')

    if file.filename == '':
        return error_response(70001, '没有选择文件')

    # 检查文件类型
    allowed_extensions = {'jpg', 'jpeg', 'png', 'gif'}
    if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
        return error_response(70002, '文件格式不支持')

    # 检查文件大小（5MB）
    file.seek(0, 2)  # 移动到文件末尾
    file_size = file.tell()
    file.seek(0)  # 重置到文件开头

    if file_size > 5 * 1024 * 1024:
        return error_response(70003, '文件大小超限')

    # 模拟文件上传（实际项目中应上传到云存储）
    filename = f"{datetime.now().strftime('%Y%m%d_%H%M%S')}_{file.filename}"
    file_url = f"https://cdn.petservice.com/images/{filename}"

    return success_response({
        'file_url': file_url,
        'file_size': file_size,
        'file_type': file.content_type
    }, '上传成功')

@app.route('/api/available-times', methods=['GET'])
def get_available_times():
    """获取可用时间段"""
    date = request.args.get('date')
    service_id = request.args.get('service_id')
    location = request.args.get('location')

    if not date:
        return error_response(400, '日期参数不能为空')

    # 模拟可用时间段（实际项目中应根据商家营业时间和已预约时间计算）
    all_times = ['09:00', '10:00', '11:00', '14:00', '15:00', '16:00', '17:00', '18:00']

    # 查找该日期已预约的时间
    booked_times = []
    for appointment in appointments_db.values():
        if appointment['appointment_time'].startswith(date):
            time_part = appointment['appointment_time'].split('T')[1][:5]
            booked_times.append(time_part)

    # 过滤已预约的时间
    available_times = [time for time in all_times if time not in booked_times]

    return success_response({
        'date': date,
        'available_times': available_times
    }, '获取成功')

if __name__ == '__main__':
    # 初始化一些测试数据
    print("正在初始化测试数据...")
    
    # 添加测试商家（严格按照数据库表结构）
    merchant_id = generate_id('M')
    merchants_db[merchant_id] = {
        'merchant_id': merchant_id,
        'merchant_account': 'petshop001',
        'merchant_password': hash_password('123456'),
        'merchant_name': '爱宠美容店',
        'merchant_phone': '***********',
        'merchant_location': '北京市朝阳区xxx街道'
    }

    # 添加测试服务（严格按照数据库表结构）
    service_id = generate_id('S', 11)
    services_db[service_id] = {
        'service_id': service_id,
        'service_name': '宠物基础美容套餐',
        'service_kind': 0,
        'service_description': '包含洗澡、修剪毛发、清洁耳朵等服务',
        'service_price': 88.00,
        'service_time': 120,
        'service_picture': 'url1,url2'
    }
    
    print(f"测试商家账号: petshop001, 密码: 123456")
    print(f"测试服务ID: {service_id}")
    print("Flask 应用启动成功！")
    print("API 基础地址: http://localhost:5000/api")
    print("可以在 APIFOX 中测试以下接口:")
    print("- POST /api/auth/send-code")
    print("- POST /api/auth/register")
    print("- POST /api/auth/login")
    print("- PUT /api/user/profile")
    print("- POST /api/pets")
    print("- GET /api/pets")
    print("- DELETE /api/pets/{pet_id}")
    
    app.run(debug=True, host='0.0.0.0', port=5000)
