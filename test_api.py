#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API测试脚本
用于验证宠物服务平台API的基本功能
"""

import requests
import json
import time

# API基础地址
BASE_URL = "http://localhost:5000/api"

def test_api():
    print("🚀 开始测试宠物服务平台API...")
    print("=" * 50)
    
    # 测试1: 获取服务列表
    print("📋 测试1: 获取服务列表")
    try:
        response = requests.get(f"{BASE_URL}/services")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 成功! 找到 {data['data']['total']} 个服务")
            if data['data']['services']:
                service = data['data']['services'][0]
                print(f"   示例服务: {service['service_name']} - ¥{service['service_price']}")
        else:
            print(f"❌ 失败! 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试2: 发送验证码
    print("📱 测试2: 发送短信验证码")
    try:
        response = requests.post(f"{BASE_URL}/auth/send-code", 
                               json={"phone": "13800138000"})
        if response.status_code == 200:
            print("✅ 验证码发送成功!")
            print("   注意: 验证码会在Flask应用控制台显示")
        else:
            print(f"❌ 失败! 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试3: 用户注册（使用固定验证码进行演示）
    print("👤 测试3: 用户注册")
    try:
        # 先发送验证码
        requests.post(f"{BASE_URL}/auth/send-code", json={"phone": "13900139000"})
        time.sleep(1)  # 等待1秒
        
        # 注册用户（使用任意6位数字作为验证码，因为是演示）
        response = requests.post(f"{BASE_URL}/auth/register", 
                               json={"phone": "13900139000", "verification_code": "123456"})
        if response.status_code == 200:
            data = response.json()
            user_token = data['data']['token']
            print("✅ 用户注册成功!")
            print(f"   用户ID: {data['data']['user_id']}")
            print(f"   Token: {user_token[:20]}...")
            
            # 测试4: 添加宠物
            print()
            print("🐕 测试4: 添加宠物")
            headers = {"Authorization": f"Bearer {user_token}"}
            pet_data = {
                "pet_name": "小白",
                "pet_kind": "狗",
                "pet_age": "2022-01-01",
                "pet_weight": "5.5",
                "pet_gender": 0
            }
            response = requests.post(f"{BASE_URL}/pets", json=pet_data, headers=headers)
            if response.status_code == 200:
                pet_info = response.json()['data']
                print("✅ 宠物添加成功!")
                print(f"   宠物ID: {pet_info['pet_id']}")
                print(f"   宠物名: {pet_info['pet_name']}")
                
                # 测试5: 获取宠物列表
                print()
                print("📝 测试5: 获取宠物列表")
                response = requests.get(f"{BASE_URL}/pets", headers=headers)
                if response.status_code == 200:
                    pets = response.json()['data']
                    print(f"✅ 成功! 用户有 {len(pets)} 只宠物")
                else:
                    print(f"❌ 失败! 状态码: {response.status_code}")
            else:
                print(f"❌ 宠物添加失败! 状态码: {response.status_code}")
        else:
            print(f"❌ 注册失败! 状态码: {response.status_code}")
            print(f"   响应: {response.text}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试6: 商家登录
    print("🏪 测试6: 商家登录")
    try:
        response = requests.post(f"{BASE_URL}/merchant/auth/login", 
                               json={"merchant_account": "petshop001", "merchant_password": "123456"})
        if response.status_code == 200:
            data = response.json()
            merchant_token = data['data']['token']
            print("✅ 商家登录成功!")
            print(f"   商家名: {data['data']['merchant_info']['merchant_name']}")
            print(f"   Token: {merchant_token[:20]}...")
            
            # 测试7: 获取商家服务列表
            print()
            print("🛍️ 测试7: 获取商家服务列表")
            headers = {"Authorization": f"Bearer {merchant_token}"}
            response = requests.get(f"{BASE_URL}/merchant/services", headers=headers)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 成功! 商家有 {data['data']['total']} 个服务")
            else:
                print(f"❌ 失败! 状态码: {response.status_code}")
        else:
            print(f"❌ 商家登录失败! 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    
    # 测试8: 获取可用时间段
    print("⏰ 测试8: 获取可用时间段")
    try:
        response = requests.get(f"{BASE_URL}/available-times?date=2024-01-02")
        if response.status_code == 200:
            data = response.json()
            times = data['data']['available_times']
            print(f"✅ 成功! 2024-01-02 有 {len(times)} 个可用时间段")
            print(f"   可用时间: {', '.join(times[:3])}...")
        else:
            print(f"❌ 失败! 状态码: {response.status_code}")
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    print()
    print("=" * 50)
    print("🎉 API测试完成!")
    print()
    print("📖 接下来你可以:")
    print("1. 在APIFOX中导入API进行详细测试")
    print("2. 查看 APIFOX_Test_Guide.md 获取完整测试指南")
    print("3. 使用预置的商家账号: petshop001 / 123456")
    print("4. API基础地址: http://localhost:5000/api")

if __name__ == "__main__":
    test_api()
