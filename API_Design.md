# 宠物服务平台 API 接口设计文档

## 基础信息

- **Base URL**: `https://api.petservice.com/v1`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 通用响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 状态码说明

- 200: 成功
- 400: 请求参数错误
- 401: 未授权
- 403: 禁止访问
- 404: 资源不存在
- 500: 服务器内部错误

---

## 用户端 API

### 1. 用户认证模块

#### 1.1 发送验证码
```
POST /auth/send-code
```

**请求参数:**
```json
{
  "phone": "***********"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "验证码发送成功",
  "data": {
    "expire_time": 300
  }
}
```

#### 1.2 用户注册
```
POST /auth/register
```

**请求参数:**
```json
{
  "phone": "***********",
  "verification_code": "123456"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "user_id": "U0000001",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "user_id": "U0000001",
      "user_phone": "***********",
      "user_name": "***********",
      "user_sex": null
    }
  }
}
```

#### 1.3 用户登录
```
POST /auth/login
```

**请求参数:**
```json
{
  "phone": "***********",
  "verification_code": "123456"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "user_id": "U0000001",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user_info": {
      "user_id": "U0000001",
      "user_phone": "***********",
      "user_name": "用户昵称",
      "user_sex": 0
    }
  }
}
```

#### 1.4 更新用户信息
```
PUT /user/profile
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "user_name": "新昵称",
  "user_sex": 0
}
```

**响应:**
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "user_id": "U0000001",
    "user_phone": "***********",
    "user_name": "新昵称",
    "user_sex": 0
  }
}
```

### 2. 宠物管理模块

#### 2.1 添加宠物
```
POST /pets
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "pet_kind": "狗",
  "pet_age": "2022-01-01",
  "pet_weight": "5.5"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "pet_id": "P0000001",
    "pet_kind": "狗",
    "pet_age": "2022-01-01",
    "pet_weight": "5.5",
    "user_id": "U0000001"
  }
}
```

#### 2.2 获取宠物列表
```
GET /pets
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "pet_id": "P0000001",
      "pet_kind": "狗",
      "pet_age": "2022-01-01",
      "pet_weight": "5.5",
      "user_id": "U0000001"
    }
  ]
}
```

#### 2.3 删除宠物
```
DELETE /pets/{pet_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 3. 服务模块

#### 3.1 获取服务列表
```
GET /services
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `service_kind`: 服务类型 (0-美容，1-寄养，2-清洁，3-训练)
- `keyword`: 搜索关键词

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "services": [
      {
        "service_id": "S00000001",
        "service_name": "宠物基础美容套餐",
        "service_kind": 0,
        "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
        "service_price": 88.00,
        "service_time": 120,
        "service_picture": ["url1", "url2"]
      }
    ]
  }
}
```

#### 3.2 获取服务详情
```
GET /services/{service_id}
```

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "service_id": "S00000001",
    "service_name": "宠物基础美容套餐",
    "service_kind": 0,
    "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
    "service_price": 88.00,
    "service_time": 120,
    "service_picture": ["url1", "url2"]
  }
}
```

### 4. 订单模块

#### 4.1 创建订单
```
POST /orders
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "service_id": "S00000001",
  "pet_id": "P0000001"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "order_id": "O0000000001",
    "user_id": "U0000001",
    "service_id": "S00000001",
    "order_status": 4,
    "order_price": 88.00,
    "pet_id": "P0000001",
    "payment_url": "https://pay.alipay.com/..."
  }
}
```

#### 4.2 订单支付回调
```
POST /orders/{order_id}/payment-callback
```

**请求参数:**
```json
{
  "payment_id": "PAY00000001",
  "payment_status": 1,
  "payment_way": 0,
  "payment_price": 88.00,
  "trade_no": "alipay_trade_no"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "支付成功",
  "data": {
    "order_id": "O0000000001",
    "order_status": 1
  }
}
```

#### 4.3 获取订单列表
```
GET /orders
```

**请求头:**
```
Authorization: Bearer {token}
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `order_status`: 订单状态 (0-已取消，1-已支付，2-已预约，3-已完成，4-未支付)
- `keyword`: 搜索关键词

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "orders": [
      {
        "order_id": "O0000000001",
        "user_id": "U0000001",
        "service_id": "S00000001",
        "service_name": "宠物基础美容套餐",
        "order_status": 1,
        "order_price": 88.00,
        "pet_id": "P0000001",
        "pet_kind": "狗",
        "create_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

#### 4.4 获取订单详情
```
GET /orders/{order_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "order_id": "O0000000001",
    "user_id": "U0000001",
    "service_id": "S00000001",
    "service_name": "宠物基础美容套餐",
    "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
    "order_status": 1,
    "order_price": 88.00,
    "pet_id": "P0000001",
    "pet_kind": "狗",
    "pet_weight": "5.5",
    "pet_age": "2022-01-01",
    "create_time": "2024-01-01T10:00:00Z",
    "appointment_info": {
      "appointment_id": "A0000000001",
      "appointment_time": "2024-01-02T14:00:00Z",
      "appointment_location": "北京市朝阳区宠物店",
      "appointment_status": 1
    }
  }
}
```

#### 4.5 取消订单
```
PUT /orders/{order_id}/cancel
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "订单取消成功",
  "data": {
    "order_id": "O0000000001",
    "order_status": 0,
    "refund_amount": 88.00
  }
}
```

### 5. 预约模块

#### 5.1 创建预约
```
POST /appointments
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "order_id": "O0000000001",
  "appointment_time": "2024-01-02T14:00:00Z",
  "appointment_location": "北京市朝阳区宠物店"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约成功",
  "data": {
    "appointment_id": "A0000000001",
    "order_id": "O0000000001",
    "appointment_time": "2024-01-02T14:00:00Z",
    "appointment_location": "北京市朝阳区宠物店",
    "appointment_status": 0,
    "user_id": "U0000001"
  }
}
```

#### 5.2 修改预约
```
PUT /appointments/{appointment_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "appointment_time": "2024-01-03T15:00:00Z",
  "appointment_location": "北京市朝阳区宠物店"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约修改成功",
  "data": {
    "appointment_id": "A0000000001",
    "order_id": "O0000000001",
    "appointment_time": "2024-01-03T15:00:00Z",
    "appointment_location": "北京市朝阳区宠物店",
    "appointment_status": 0,
    "user_id": "U0000001"
  }
}
```

#### 5.3 取消预约
```
PUT /appointments/{appointment_id}/cancel
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约取消成功",
  "data": {
    "appointment_id": "A0000000001",
    "appointment_status": 2
  }
}
```

### 6. 评价模块

#### 6.1 提交评价
```
POST /evaluations
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "order_id": "O0000000001",
  "evaluation_star": 5,
  "evaluation_context": "服务很好，宠物很满意",
  "evaluation_picture": ["url1", "url2"]
}
```

**响应:**
```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": {
    "evaluation_id": "E0000000001",
    "user_id": "U0000001",
    "order_id": "O0000000001",
    "evaluation_star": 5,
    "evaluation_context": "服务很好，宠物很满意",
    "evaluation_picture": ["url1", "url2"],
    "create_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 6.2 获取服务评价列表
```
GET /services/{service_id}/evaluations
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 20,
    "page": 1,
    "limit": 10,
    "average_star": 4.5,
    "evaluations": [
      {
        "evaluation_id": "E0000000001",
        "user_name": "用户昵称",
        "evaluation_star": 5,
        "evaluation_context": "服务很好，宠物很满意",
        "evaluation_picture": ["url1", "url2"],
        "create_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

---

## 商家端 API

### 1. 商家认证模块

#### 1.1 商家注册
```
POST /merchant/auth/register
```

**请求参数:**
```json
{
  "merchant_account": "petshop001",
  "merchant_password": "password123",
  "merchant_name": "爱宠美容店",
  "merchant_phone": "***********",
  "merchant_location": "北京市朝阳区xxx街道"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "注册成功",
  "data": {
    "merchant_id": "M0000001",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "merchant_info": {
      "merchant_id": "M0000001",
      "merchant_account": "petshop001",
      "merchant_name": "爱宠美容店",
      "merchant_phone": "***********",
      "merchant_location": "北京市朝阳区xxx街道"
    }
  }
}
```

#### 1.2 商家登录
```
POST /merchant/auth/login
```

**请求参数:**
```json
{
  "merchant_account": "petshop001",
  "merchant_password": "password123"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "merchant_id": "M0000001",
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "merchant_info": {
      "merchant_id": "M0000001",
      "merchant_account": "petshop001",
      "merchant_name": "爱宠美容店",
      "merchant_phone": "***********",
      "merchant_location": "北京市朝阳区xxx街道"
    }
  }
}
```

#### 1.3 更新商家信息
```
PUT /merchant/profile
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "merchant_name": "新店名",
  "merchant_phone": "***********",
  "merchant_location": "新地址",
  "merchant_password": "newpassword123"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "merchant_id": "M0000001",
    "merchant_account": "petshop001",
    "merchant_name": "新店名",
    "merchant_phone": "***********",
    "merchant_location": "新地址"
  }
}
```

### 2. 服务管理模块

#### 2.1 添加服务
```
POST /merchant/services
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "service_name": "宠物基础美容套餐",
  "service_kind": 0,
  "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
  "service_price": 88.00,
  "service_time": 120,
  "service_picture": ["url1", "url2"],
  "pet_requirements": {
    "min_weight": 1.0,
    "max_weight": 50.0,
    "min_age_months": 3,
    "max_age_months": 120,
    "allowed_kinds": ["狗", "猫"]
  }
}
```

**响应:**
```json
{
  "code": 200,
  "message": "服务添加成功",
  "data": {
    "service_id": "S00000001",
    "service_name": "宠物基础美容套餐",
    "service_kind": 0,
    "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
    "service_price": 88.00,
    "service_time": 120,
    "service_picture": ["url1", "url2"],
    "merchant_id": "M0000001",
    "create_time": "2024-01-01T10:00:00Z"
  }
}
```

#### 2.2 获取商家服务列表
```
GET /merchant/services
```

**请求头:**
```
Authorization: Bearer {token}
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `service_kind`: 服务类型
- `keyword`: 搜索关键词

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 30,
    "page": 1,
    "limit": 10,
    "services": [
      {
        "service_id": "S00000001",
        "service_name": "宠物基础美容套餐",
        "service_kind": 0,
        "service_description": "包含洗澡、修剪毛发、清洁耳朵等服务",
        "service_price": 88.00,
        "service_time": 120,
        "service_picture": ["url1", "url2"],
        "create_time": "2024-01-01T10:00:00Z",
        "order_count": 15,
        "average_rating": 4.5
      }
    ]
  }
}
```

#### 2.3 编辑服务
```
PUT /merchant/services/{service_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "service_name": "宠物高级美容套餐",
  "service_kind": 0,
  "service_description": "包含洗澡、修剪毛发、清洁耳朵、指甲修剪等服务",
  "service_price": 128.00,
  "service_time": 180,
  "service_picture": ["url1", "url2", "url3"]
}
```

**响应:**
```json
{
  "code": 200,
  "message": "服务更新成功",
  "data": {
    "service_id": "S00000001",
    "service_name": "宠物高级美容套餐",
    "service_kind": 0,
    "service_description": "包含洗澡、修剪毛发、清洁耳朵、指甲修剪等服务",
    "service_price": 128.00,
    "service_time": 180,
    "service_picture": ["url1", "url2", "url3"],
    "update_time": "2024-01-01T11:00:00Z"
  }
}
```

#### 2.4 删除服务
```
DELETE /merchant/services/{service_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "服务删除成功",
  "data": null
}
```

### 3. 订单管理模块

#### 3.1 获取订单列表
```
GET /merchant/orders
```

**请求头:**
```
Authorization: Bearer {token}
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `order_status`: 订单状态
- `start_date`: 开始日期
- `end_date`: 结束日期
- `keyword`: 搜索关键词（用户手机号、订单号）

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 100,
    "page": 1,
    "limit": 10,
    "orders": [
      {
        "order_id": "O0000000001",
        "user_phone": "***********",
        "service_name": "宠物基础美容套餐",
        "order_status": 2,
        "order_price": 88.00,
        "pet_kind": "狗",
        "pet_weight": "5.5",
        "create_time": "2024-01-01T10:00:00Z",
        "appointment_time": "2024-01-02T14:00:00Z",
        "appointment_location": "北京市朝阳区宠物店"
      }
    ]
  }
}
```

#### 3.2 确认订单
```
PUT /merchant/orders/{order_id}/confirm
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "订单确认成功",
  "data": {
    "order_id": "O0000000001",
    "order_status": 2
  }
}
```

#### 3.3 完成订单
```
PUT /merchant/orders/{order_id}/complete
```

**请求头:**
```
Authorization: Bearer {token}
```

**响应:**
```json
{
  "code": 200,
  "message": "订单完成",
  "data": {
    "order_id": "O0000000001",
    "order_status": 3
  }
}
```

#### 3.4 取消订单
```
PUT /merchant/orders/{order_id}/cancel
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "cancel_reason": "商家临时有事，无法提供服务"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "订单取消成功",
  "data": {
    "order_id": "O0000000001",
    "order_status": 0,
    "refund_amount": 105.6,
    "compensation_rate": 1.2
  }
}
```

### 4. 预约管理模块

#### 4.1 获取预约列表
```
GET /merchant/appointments
```

**请求头:**
```
Authorization: Bearer {token}
```

**查询参数:**
- `page`: 页码 (默认: 1)
- `limit`: 每页数量 (默认: 10)
- `appointment_status`: 预约状态
- `date`: 指定日期 (YYYY-MM-DD)
- `keyword`: 搜索关键词

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "appointments": [
      {
        "appointment_id": "A0000000001",
        "order_id": "O0000000001",
        "user_phone": "***********",
        "service_name": "宠物基础美容套餐",
        "appointment_time": "2024-01-02T14:00:00Z",
        "appointment_location": "北京市朝阳区宠物店",
        "appointment_status": 0,
        "pet_kind": "狗",
        "pet_weight": "5.5",
        "create_time": "2024-01-01T10:00:00Z"
      }
    ]
  }
}
```

#### 4.2 确认预约
```
PUT /merchant/appointments/{appointment_id}/confirm
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "notes": "请准时到达，带好宠物用品"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约确认成功",
  "data": {
    "appointment_id": "A0000000001",
    "appointment_status": 1,
    "notes": "请准时到达，带好宠物用品"
  }
}
```

#### 4.3 修改预约
```
PUT /merchant/appointments/{appointment_id}
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "appointment_time": "2024-01-03T15:00:00Z",
  "appointment_location": "北京市朝阳区宠物店",
  "notes": "时间调整通知"
}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约修改成功",
  "data": {
    "appointment_id": "A0000000001",
    "appointment_time": "2024-01-03T15:00:00Z",
    "appointment_location": "北京市朝阳区宠物店",
    "notes": "时间调整通知"
  }
}
```

#### 4.4 取消预约
```
PUT /merchant/appointments/{appointment_id}/cancel
```

**请求头:**
```
Authorization: Bearer {token}
```

**请求参数:**
```json
{
  "cancel_reason": "商家临时有事，无法提供服务",
  "compensation_rate": 1.2
}
```

**响应:**
```json
{
  "code": 200,
  "message": "预约取消成功",
  "data": {
    "appointment_id": "A0000000001",
    "appointment_status": 2,
    "refund_amount": 105.6,
    "compensation_rate": 1.2
  }
}
```

### 5. 数据统计模块

#### 5.1 获取商家统计数据
```
GET /merchant/statistics
```

**请求头:**
```
Authorization: Bearer {token}
```

**查询参数:**
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_orders": 150,
    "completed_orders": 120,
    "cancelled_orders": 10,
    "pending_orders": 20,
    "total_revenue": 12800.00,
    "average_rating": 4.5,
    "service_statistics": [
      {
        "service_name": "宠物基础美容套餐",
        "order_count": 50,
        "revenue": 4400.00,
        "average_rating": 4.6
      }
    ]
  }
}
```

---

## 通用工具接口

### 1. 文件上传
```
POST /upload
```

**请求头:**
```
Authorization: Bearer {token}
Content-Type: multipart/form-data
```

**请求参数:**
- `file`: 文件 (图片格式: jpg, png, gif，大小限制: 5MB)
- `type`: 文件类型 (service_image, evaluation_image, avatar)

**响应:**
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "file_url": "https://cdn.petservice.com/images/xxx.jpg",
    "file_size": 1024000,
    "file_type": "image/jpeg"
  }
}
```

### 2. 获取可用时间段
```
GET /available-times
```

**查询参数:**
- `date`: 日期 (YYYY-MM-DD)
- `service_id`: 服务ID
- `location`: 服务地点

**响应:**
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "date": "2024-01-02",
    "available_times": [
      "09:00",
      "10:00",
      "11:00",
      "14:00",
      "15:00",
      "16:00"
    ]
  }
}
```

---

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 10001 | 手机号格式错误 |
| 10002 | 验证码错误或已过期 |
| 10003 | 用户不存在 |
| 10004 | 用户已存在 |
| 10005 | 密码错误 |
| 20001 | 宠物不存在 |
| 20002 | 宠物信息不符合服务要求 |
| 30001 | 服务不存在 |
| 30002 | 服务已下架 |
| 40001 | 订单不存在 |
| 40002 | 订单状态不允许此操作 |
| 40003 | 支付失败 |
| 50001 | 预约时间不可用 |
| 50002 | 预约不存在 |
| 50003 | 预约时间冲突 |
| 60001 | 商家不存在 |
| 60002 | 商家账号或密码错误 |
| 70001 | 文件上传失败 |
| 70002 | 文件格式不支持 |
| 70003 | 文件大小超限 |
